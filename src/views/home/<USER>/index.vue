<template>
  <BuseCrud
    ref="crud"
    title=""
    :loading="loading"
    :filterOptions="filterOptions"
    :tablePage="tablePage"
    :tableColumn="tableColumn"
    :tableData="tableData"
    :modalConfig="modalConfig"
    @modalCancel="modalCancelHandler"
    @modalSubmit="modalSubmit"
    @modalConfirm="modalConfirmHandler"
    @rowDel="deleteRowHandler"
    @loadData="loadData"
    @handleCreate="rowAdd"
    @handleReset="handleReset"
    @rowEdit="rowEdit"
  >
    <template #defaultHeader>
      <span class="button-right" @click="getSelectEvent">
        <a-button @click="exportList">导出</a-button>
      </span>
    </template>

    <template #datePick>
      <a-range-picker
        style="width: 268px"
        show-time
        v-model="params.bridgeTime"
        @change="changeTime(params.bridgeTime)"
      />
    </template>

    <template #checkScore>
      <a-inputNumber
        @change="startScoreChange(params)"
        v-model="params.startScore"
        placeholder="请输入最小分数"
        style="width: 48%; margin-right: 4%"
      ></a-inputNumber>
      <a-inputNumber
        @change="endScoreChange(params)"
        v-model="params.endScore"
        placeholder="请输入最大分数"
        style="width: 48%"
      ></a-inputNumber>
    </template>
  </BuseCrud>
</template>

<script>
// import mock from './mock1';
import { checkAPI, checkAppealAPI, checkDetailAPI, checkListAPI, exportAPI } from '@/api/system/telManager';
// import { exportAPI } from '@/api/system/guestList';
import { downLoadXlsx } from '@/utils/system/common';
import { DictCodeAPI } from '@/api/system/dict';
import moment from 'moment';
import 'moment/locale/zh-cn';
moment.locale('zh-cn');
export default {
  name: 'DumiDocVueIndex',
  data() {
    return {
      params: {
        communicateId: '',
        bridgeTime: '',
        startScore: '',
        endScore: '',
        clientName: '',
        classify: '',
        startTime: '',
        endTime: '',
        checkStatus: '',
        communicateStatusFlag: '',
        callStatus: '',
        customerNumber: '',
      },
      menuShow: true,
      startScore: '',
      endScore: '',
      tableData: [],
      tableColumn: [
        { type: 'checkbox' },
        //列名，列数字转文字配置
        {
          field: 'communicateId',
          title: '通话编号',
          minWidth: 167,
        },
        {
          field: 'customerNumber',
          title: '访客电话',
          minWidth: 150,
        },
        {
          field: 'clientName',
          title: '坐席名称',
          minWidth: 150,
        },
        {
          field: 'clientName',
          title: '账号名称',
          minWidth: 150,
        },
        {
          field: 'communicateStatusFlag',
          title: '通话状态',
          minWidth: 180,
          formatter: ({ cellValue }) => {
            if (cellValue == '1') {
              return '已接通';
            } else if (cellValue == '2') {
              return '未接通-工作时间';
            } else if (cellValue == '3') {
              return '未接通-非工作时间';
            }
          },
        },
        {
          field: 'startTime',
          title: '呼入时间',
          minWidth: 170,
        },
        {
          field: 'startTime',
          title: '通话时长',
          minWidth: 170,
        },
        {
          field: 'checkScore',
          title: '质检分数',
          minWidth: 80,
        },
        {
          field: 'checkStatus',
          title: '质检状态',
          minWidth: 80,
        },
        {
          field: 'checkStatus',
          title: '质检人',
          minWidth: 80,
        },
        {
          field: 'hotLine',
          title: '被叫热线',
          minWidth: 120,
        },
        {
          field: 'hotLine',
          title: '通话记录数据来源',
          minWidth: 120,
        },
      ],
      checkStatus: [],
      communicateStatusFlag: [],
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
    };
  },
  computed: {
    filterOptions() {
      //筛选类配置
      return {
        config: [
          {
            field: 'communicateId',
            title: '通话编号',
          },
          {
            field: 'checkStatus',
            title: '质检状态',
            element: 'a-select',
            props: {
              options: this.checkStatus,
            },
          },
          {
            field: 'bridgeTime',
            title: '通话时间',
            element: 'slot',
            slotName: 'datePick',
          },

          {
            field: 'clientName',
            title: '坐席',
          },
          {
            field: 'communicateStatusFlag',
            title: '通话状态',
            element: 'a-select',
            props: {
              options: this.communicateStatusFlag,
            },
          },
          {
            field: 'checkScore',
            title: '质检分数',
            element: 'slot',
            slotName: 'checkScore',
          },
          {
            field: 'customerNumber',
            title: '账号名称',
          },
          {
            field: 'customerNumber',
            title: '访客电话',
          },
          {
            field: 'customerNumber',
            title: '质检人',
          },
          // {
          //   field: 'classify',
          //   title: '工单分类',
          // },
          {
            field: 'secScore',
            title: '质检分数末尾',
            show: false,
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      //弹窗类配置
      return {
        formConfig: [
          { title: '访客电话', field: 'customerNumber' },
          { title: '通话类型', field: 'customerNumber' },
          { title: '坐席名称', field: 'customerNumber' },
          { title: '通话状态', field: 'customerNumber' },
          { title: '账户名称', field: 'customerNumber' },
          { title: '归属地', field: 'customerNumber' },
          { title: '呼入时间', field: 'customerNumber' },
          { title: '通话时长', field: 'customerNumber' },
        ],
        customOperationTypes: [
          {
            title: '查看',
            typeName: 'goView',
            event: (row) => {
              return new Promise((resolve) => {
                this.$store.dispatch('base/cumIdFn', row.communicateId);
                this.$store.dispatch('base/mainIdFn', row.mainUniqueId);
                this.$store.dispatch('base/numberFn', row.customerNumber);
                // this.$store.dispatch('base/orderIdFn', [row.orderId]);
                // console.log('看看orderId', this.$sotre.state.base.orderId);

                try {
                  let arr = [];
                  let arrStatus = [];
                  let arrTime = [];
                  for (const item of row.orderInfos) {
                    console.log('数组', item);
                    arr.push(item.orderId);
                    arrStatus.push(item.orderStatus);
                    arrTime.push(item.operationTime);
                  }
                  console.log('新添加的info的ID数组', arr);
                  this.$store.dispatch('base/orderIdFn', arr);
                  this.$store.dispatch('base/statusFn', arrStatus);
                  this.$store.dispatch('base/timeFn', arrTime);
                  console.log('storeOrderId', this.$store.state.base.orderId);
                  console.log('orderId的状态数组', this.$store.state.base.arrStatus);
                } catch (err) {
                  console.log('这个里面没有orderId数组');
                  this.$store.dispatch('base/orderIdFn', []);
                  this.$store.dispatch('base/statusFn', []);
                  this.$store.dispatch('base/timeFn', []);
                }

                console.log('row是哪些', row);
                this.$router.push('/telmanagerdetail');
                resolve();
              });
            },
          },
        ],
        menuTitle: '操作',
        addBtn: true,
        delBtn: true,
        viewBtn: false,
        editBtn: true,
      };
    },
  },
  mounted() {
    this.getDict();
  },
  methods: {
    // changePage() {
    //   console.log('换页了');
    // },
    rowEdit(row) {
      this.$refs.crud.switchModalView(true, 'UPDATE', row);
    },
    //筛选重置按钮
    handleReset() {
      this.tablePage.currentPage = 1;
      this.params = {
        communicateId: '',
        bridgeTime: '',
        startScore: '',
        endScore: '',
        clientName: '',
        classify: '',
        startTime: '',
        endTime: '',
        checkStatus: '',
        communicateStatusFlag: '',
        callStatus: '',
        customerNumber: '',
      };
      this.loadData();
    },
    async loadData() {
      console.log('params筛选参数', this.params);
      this.loading = true;
      const result = await this.checkListFn();
      this.loading = false;
      this.tableData = result.data;
      console.log('看看表里的数据', this.tableData);
      this.tablePage.total = result.count;
    },
    modalConfirmHandler(tableList) {
      if (tableList.crudOperationType == 'update') {
        console.log('编辑成功');
      } else if (tableList.crudOperationType == 'add') {
        console.log(tableList);
        console.log('添加成功');
      }
    },
    modalSubmit() {
      console.log('提交按钮');
    },
    modalCancelHandler() {
      console.log('取消按钮');
    },
    deleteRowHandler() {},
    rowAdd() {
      this.$refs.crud.switchModalView(true, 'ADD');
    },
    getSelectEvent() {
      // console.log('导出');
      // let selectRecords = this.$refs.crud.getCheckboxRecords();
      // console.log(selectRecords);
      // this.$message.info(`查询参数为：${JSON.stringify(selectRecords)}`);
    },
    async checkListFn() {
      let page = { pageSize: this.tablePage.pageSize, pageNum: this.tablePage.currentPage };
      let [result] = await checkListAPI({
        pageFlag: '1',
        ...page,
        ...this.params,
      });
      console.log('质检列表', result.data);
      return result;
    },
    //测试接口
    async testAPI() {
      let id = '20230921165845582371324861205017';
      let project = [{ checkTemplateId: '20230924022928883001778465070860', projectScore: '60' }];
      let [resultA] = await checkAPI({ orderId: id, project });
      console.log('check接口', resultA);

      let [resultB] = await checkDetailAPI('20230926143246344095070329883229');
      console.log('checkDetail接口', resultB);

      let [resultC] = await checkAppealAPI();
      console.log(resultC);
    },
    async exportList() {
      let page = { pageSize: this.tablePage.pageSize, pageNum: this.tablePage.currentPage };
      const communicateIds = this.$refs.crud.getCheckboxRecords()?.map((x) => x.communicateId);
      console.log('communicateIds', communicateIds);
      let [result] = await exportAPI({ ...this.params, ...page, pageFlag: '0', communicateIds: communicateIds });
      downLoadXlsx(new Blob([result]), '通话列表导出.xlsx');
    },

    startScoreChange(value) {
      console.log(value);
    },
    endScoreChange(value) {
      console.log(value);
    },
    changeTime(value) {
      const [startDate, endDate] = value;
      this.params.startTime = moment(startDate).format('YYYY-MM-DD HH:mm:ss');
      this.params.endTime = moment(endDate).format('YYYY-MM-DD HH:mm:ss');
      console.log('时间', this.params.startTime);
      console.log('时间', this.params.endTime);
    },
    getDict() {
      DictCodeAPI('check_status').then((res) => {
        this.checkStatus = res;
      });
      console.log('字典', this.checkStatus);

      DictCodeAPI('communicate_status_flag').then((res) => {
        this.communicateStatusFlag = res;
      });
      // let resA = await DictCodeAPI('');communicate_status_flag
    },
  },
  beforeMount() {
    this.loadData();
  },
};
</script>

<style lang="less" scoped>
.button-right {
  margin-right: 16px;
}
.tag-place {
  display: flex;
  justify-content: flex-end;
  top: -36px;
  position: relative;
  .tag {
    position: absolute;
    height: 32px;
    line-height: 32px;
  }
}
.name-button {
  margin-right: 16px;
}
.guest-tag {
  display: flex;
  justify-content: space-around;
  .tags {
    white-space: nowrap;
    line-height: 32px;
    height: 32px;
  }
}
.icon-plus {
  margin-right: 4px;
}
//质检按钮
.check-router {
  display: inline-block;
  margin-right: 10px;
  color: #165dff;
  cursor: pointer;
  padding: 0;
  min-width: 38px;
  height: 30px;
  line-height: 30px;
}
</style>
